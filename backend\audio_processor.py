import librosa
import numpy as np
from typing import Optional
import os
import tempfile
import yt_dlp
import requests

def analyze_audio(file_path: Optional[str] = None, url: Optional[str] = None) -> dict:
    """
    Analyze audio file/URL and generate music prompt
    Returns dict with 'prompt' and 'features' keys
    """
    # Download from URL if provided
    if url and not file_path:
        file_path = download_audio(url)
    
    if not file_path or not os.path.exists(file_path):
        raise ValueError("No valid audio file provided")

    # Load audio file
    y, sr = librosa.load(file_path, duration=30)  # Analyze first 30 seconds
    
    # Extract features
    features = {
        'tempo': extract_tempo(y, sr),
        'key': extract_key(y, sr),
        'instruments': detect_instruments(y, sr),
        'mood': detect_mood(y, sr),
        'genre': predict_genre(y, sr)
    }
    
    # Generate prompt
    prompt = generate_prompt(features)
    
    return {
        'prompt': prompt,
        'features': features
    }

def download_audio(url: str) -> str:
    """Download audio from URL and return local file path"""
    try:
        # Create a temporary file for the downloaded audio
        temp_dir = tempfile.gettempdir()
        temp_filename = f"temp_audio_{os.getpid()}.%(ext)s"
        temp_path = os.path.join(temp_dir, temp_filename)

        ydl_opts = {
            'format': 'bestaudio/best',
            'outtmpl': temp_path,
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'wav',
                'preferredquality': '192',
            }]
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])

        # Find the actual downloaded file (yt-dlp changes the extension)
        base_path = temp_path.replace('.%(ext)s', '')
        for ext in ['.wav', '.mp3', '.m4a', '.webm']:
            potential_path = base_path + ext
            if os.path.exists(potential_path):
                return potential_path

        raise ValueError("Downloaded file not found")
    except Exception as e:
        raise ValueError(f"Failed to download audio: {str(e)}")

def extract_tempo(y, sr) -> float:
    """Estimate tempo in BPM"""
    tempo, _ = librosa.beat.beat_track(y=y, sr=sr)
    return float(tempo)

def extract_key(y, sr) -> str:
    """Estimate musical key"""
    chroma = librosa.feature.chroma_stft(y=y, sr=sr)
    key = np.argmax(np.sum(chroma, axis=1))
    keys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
    return keys[key]

def detect_instruments(y, sr) -> list:
    """Detect dominant instruments (placeholder - will use Spleeter later)"""
    # TODO: Implement proper instrument separation
    return ["drums", "bass", "synth"]

def detect_mood(y, sr) -> str:
    """Detect mood from audio features"""
    # TODO: Implement proper mood detection
    return "energetic"

def predict_genre(y, sr) -> str:
    """Predict music genre (placeholder - will use ML model later)"""
    # TODO: Implement proper genre classification
    return "electronic"

def generate_prompt(features: dict) -> str:
    """Generate music prompt from extracted features"""
    return (
        f"Compose a {features['genre']} track with {features['mood']} atmosphere. "
        f"Tempo: {features['tempo']:.0f} BPM. "
        f"Key: {features['key']}. "
        f"Main instruments: {', '.join(features['instruments'])}."
    )