import React, { useState } from 'react';
import axios from 'axios';
import './App.css';

function App() {
  const [file, setFile] = useState(null);
  const [url, setUrl] = useState('');
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleUrlChange = (e) => {
    setUrl(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const formData = new FormData();
      if (file) formData.append('audio', file);
      if (url) formData.append('url', url);

      const response = await axios.post('/api/analyze', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setPrompt(response.data.prompt);
    } catch (error) {
      console.error('Error analyzing audio:', error);
      setError(error.response?.data?.detail || 'Failed to analyze audio. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="app">
      <h1>Music Prompt Generator</h1>
      <form onSubmit={handleSubmit}>
        <div className="input-group">
          <label>Upload Audio File:</label>
          <input type="file" accept="audio/*" onChange={handleFileChange} />
        </div>
        <div className="input-group">
          <label>Or Enter URL:</label>
          <input 
            type="text" 
            placeholder="YouTube, Spotify, SoundCloud URL" 
            value={url}
            onChange={handleUrlChange}
          />
        </div>
        <button type="submit" disabled={isLoading}>
          {isLoading ? 'Analyzing...' : 'Generate Prompt'}
        </button>
      </form>

      {error && (
        <div className="error-message">
          <p style={{color: 'red'}}>{error}</p>
        </div>
      )}

      {prompt && (
        <div className="prompt-result">
          <h2>Generated Music Prompt:</h2>
          <textarea readOnly value={prompt} />
          <button onClick={() => navigator.clipboard.writeText(prompt)}>
            Copy to Clipboard
          </button>
        </div>
      )}
    </div>
  );
}

export default App;