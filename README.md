# Music Prompt Generator

An application that analyzes songs and generates precise music-generation prompts for AI models.

## Features
- Audio file or URL input
- Feature extraction (tempo, key, instruments)
- Genre and mood detection
- AI prompt generation optimized for music models
- Editable prompt output

## Setup
1. Install Docker and Docker Compose
2. Clone this repository
3. Run `docker-compose up --build`

## Usage
1. Access the web interface at `http://localhost:3000`
2. Upload an audio file or paste a URL
3. View and edit the generated music prompt
4. Copy the prompt to use with AI music generators

## Technical Stack
- Frontend: React.js
- Backend: FastAPI (Python)
- Audio Processing: librosa, Spleeter
- Containerization: Docker