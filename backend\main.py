from fastapi import <PERSON><PERSON><PERSON>, UploadFile, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import os
from audio_processor import analyze_audio

app = FastAPI()

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

class AnalysisResponse(BaseModel):
    prompt: str
    features: dict

@app.post("/api/analyze")
async def analyze_music(
    audio: Optional[UploadFile] = None,
    url: Optional[str] = Form(None)
):
    if not audio and not url:
        raise HTTPException(status_code=400, detail="No input provided")
    
    try:
        # Temporary file handling for audio processing
        temp_path = None
        if audio:
            temp_path = f"temp_{audio.filename}"
            with open(temp_path, "wb") as buffer:
                buffer.write(await audio.read())
        
        # Process the audio (placeholder - will implement in audio_processor.py)
        analysis_result = analyze_audio(temp_path, url)
        
        # Clean up temp file
        if temp_path and os.path.exists(temp_path):
            os.remove(temp_path)
            
        return AnalysisResponse(
            prompt=analysis_result["prompt"],
            features=analysis_result["features"]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)